import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import useAuthStore from "../stores/authStore";

export const AuthCallback: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const { handleOAuthCallback } = useAuthStore();
  const navigate = useNavigate();

  useEffect(() => {
    const handleCallback = async () => {
      console.log("AuthCallback: Starting OAuth callback processing");

      try {
        const result = await handleOAuthCallback();

        if (result.success) {
          console.log(
            "AuthCallback: OAuth successful, redirecting to dashboard"
          );
          // Redirect to dashboard on successful authentication
          navigate("/dashboard", { replace: true });
        } else {
          console.error("AuthCallback: OAuth failed:", result.error);
          setError(result.error || "Authentication failed");

          // Redirect to login after a delay
          setTimeout(() => {
            navigate("/login", { replace: true });
          }, 5000);
        }
      } catch (error) {
        console.error("AuthCallback: Exception during processing:", error);
        setError("Failed to process authentication");

        setTimeout(() => {
          navigate("/login", { replace: true });
        }, 5000);
      }
    };

    handleCallback();
  }, [handleOAuthCallback, navigate]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background-primary">
        <div className="max-w-md w-full text-center space-y-4 p-8">
          <div className="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded">
            <h3 className="text-lg font-semibold">Authentication Error</h3>
            <p className="mt-2">{error}</p>
          </div>
          <p className="text-gray-400 mb-4">
            Redirecting to login page in 5 seconds...
          </p>
          <button
            onClick={() => navigate("/login", { replace: true })}
            className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
          >
            Go to Login Now
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background-primary">
      <div className="max-w-md w-full text-center space-y-4 p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
        <h3 className="text-lg font-semibold text-white">
          Completing Authentication...
        </h3>
        <p className="text-gray-400">Please wait while we sign you in.</p>
      </div>
    </div>
  );
};
