# Supabase Google OAuth Configuration Guide

This guide explains how to configure Google OAuth authentication in your Supabase project for the ChewyAI application.

## Prerequisites

- Supabase project created and configured
- Google Cloud Console project with OAuth credentials
- ChewyAI application deployed or running locally

## Step 1: Configure Google OAuth in Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Navigate to **APIs & Services** > **Credentials**
4. Click **Create Credentials** > **OAuth 2.0 Client IDs**
5. Configure the OAuth consent screen if not already done
6. Set **Application type** to **Web application**
7. Add authorized redirect URIs:
   - For development: `https://jpvbtrzvbpyzgtpvltss.supabase.co/auth/v1/callback`
   - For production: `https://your-project-ref.supabase.co/auth/v1/callback`
8. Save and note down the **Client ID** and **Client Secret**

## Step 2: Configure Google OAuth in Supabase Dashboard

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `jpvbtrzvbpyzgtpvltss`
3. Navigate to **Authentication** > **Providers**
4. Find **Google** in the list and click to configure
5. Enable the Google provider
6. Enter your Google OAuth credentials:
   - **Client ID**: From Google Cloud Console
   - **Client Secret**: From Google Cloud Console
7. Configure redirect URLs:
   - **Site URL**: `http://localhost:3000` (development) or your production URL
   - **Redirect URLs**: Add `http://localhost:3000/auth/callback` and your production callback URL
8. Save the configuration

## Step 3: Update Environment Variables

Ensure your backend `.env` file has the correct Supabase configuration:

```env
# Supabase Configuration
SUPABASE_URL=https://jpvbtrzvbpyzgtpvltss.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
FRONTEND_URL=http://localhost:3000
```

## Step 4: Test the OAuth Flow

1. Start your backend server: `npm run dev` (from backend directory)
2. Start your frontend server: `npm run dev` (from frontend directory)
3. Navigate to `http://localhost:3000/login`
4. Click "Sign in with Google"
5. Complete the Google OAuth flow
6. Verify you're redirected back to `/auth/callback` and then to `/dashboard`

## Troubleshooting

### Common Issues

1. **"Provider not found" error**
   - Verify Google provider is enabled in Supabase dashboard
   - Check that Client ID and Secret are correctly entered

2. **"Invalid redirect URI" error**
   - Ensure redirect URIs in Google Cloud Console match Supabase callback URL
   - Check that Site URL and Redirect URLs are configured in Supabase

3. **"Failed to exchange code for session" error**
   - Verify Client Secret is correct
   - Check that the authorization code hasn't expired

4. **User profile creation fails**
   - Ensure your database has the `users` table with proper schema
   - Check Row Level Security policies allow user creation

### Debug Steps

1. Check browser console for detailed error messages
2. Check backend server logs for OAuth processing errors
3. Verify Supabase dashboard shows OAuth attempts in Auth logs
4. Test with different Google accounts to rule out account-specific issues

## Security Considerations

- Never expose your Service Role Key in frontend code
- Use HTTPS in production for all OAuth redirects
- Regularly rotate OAuth credentials
- Monitor authentication logs for suspicious activity

## Production Deployment

When deploying to production:

1. Update Google Cloud Console with production redirect URIs
2. Update Supabase dashboard with production Site URL and Redirect URLs
3. Update environment variables with production URLs
4. Test the complete OAuth flow in production environment

## Support

If you encounter issues:

1. Check Supabase documentation: https://supabase.com/docs/guides/auth/social-login/auth-google
2. Review Google OAuth documentation: https://developers.google.com/identity/protocols/oauth2
3. Check the ChewyAI application logs for specific error messages
