import { User<PERSON>rofile, AuthR<PERSON><PERSON> } from "../shared/types";
import { safeFetch } from "./connectionService";

class AuthService {
  // Helper methods for token storage
  private setToken(token: string, rememberMe: boolean = true): void {
    // Clear any existing tokens from both storages
    localStorage.removeItem("auth_token");
    sessionStorage.removeItem("auth_token");

    // Store in appropriate storage based on user preference
    if (rememberMe) {
      localStorage.setItem("auth_token", token);
    } else {
      sessionStorage.setItem("auth_token", token);
    }
  }

  private getToken(): string | null {
    // Check localStorage first, then sessionStorage
    return (
      localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token")
    );
  }

  private clearToken(): void {
    localStorage.removeItem("auth_token");
    sessionStorage.removeItem("auth_token");
  }

  async signUp(
    email: string,
    password: string,
    name?: string
  ): Promise<AuthResult> {
    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password, name }),
      });
      const result = await response.json();
      if (result.success && result.token) {
        // Default to persistent storage for signup
        this.setToken(result.token, true);
      }
      return result;
    } catch {
      return { success: false, error: "Network error during signup" };
    }
  }

  async signIn(
    email: string,
    password: string,
    rememberMe: boolean = true
  ): Promise<AuthResult> {
    try {
      const response = await safeFetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });
      const result = await response.json();
      if (result.success && result.token) {
        this.setToken(result.token, rememberMe);
      }
      return result;
    } catch {
      return { success: false, error: "Network error during login" };
    }
  }

  async signOut(): Promise<void> {
    try {
      const token = this.getToken();
      if (token) {
        await safeFetch("/api/auth/logout", {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
        });
      }
    } finally {
      this.clearToken();
    }
  }

  async getCurrentUser(): Promise<UserProfile | null> {
    try {
      const token = this.getToken();
      if (!token) {
        return null;
      }

      const response = await safeFetch("/api/auth/user", {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.log("Token expired or invalid, clearing...");
          this.clearToken();
        }
        return null;
      }

      const result = await response.json();

      if (result.success && result.data) {
        return result.data;
      }

      return null;
    } catch (error) {
      console.error("getCurrentUser error:", error);
      return null;
    }
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  async signInWithGoogle(): Promise<{ error?: string }> {
    try {
      const redirectTo = `${window.location.origin}/auth/callback`;

      console.log("Initiating Google OAuth with redirect:", redirectTo);

      const response = await safeFetch("/api/auth/google", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ redirectTo }),
      });

      const result = await response.json();

      if (!response.ok || result.error) {
        console.error("Google OAuth initiation failed:", result);
        return {
          error:
            result.details ||
            result.error ||
            "Failed to initiate Google sign-in",
        };
      }

      // Redirect to Google OAuth URL provided by backend
      if (result.url) {
        console.log("Redirecting to Google OAuth URL");
        window.location.href = result.url;
        return {};
      }

      return { error: "No OAuth URL provided by server" };
    } catch (error) {
      console.error("Google OAuth error:", error);
      return { error: "Network error during Google sign-in" };
    }
  }

  async handleOAuthCallback(): Promise<AuthResult> {
    try {
      console.log("Processing OAuth callback...");

      // Check for error parameters first
      const urlParams = new URLSearchParams(window.location.search);
      const error = urlParams.get("error");
      const errorDescription = urlParams.get("error_description");

      if (error) {
        console.error("OAuth error received:", { error, errorDescription });
        return {
          success: false,
          error: errorDescription || error || "OAuth authentication failed",
        };
      }

      // Get authorization code from URL parameters (PKCE flow)
      const code = urlParams.get("code");
      const state = urlParams.get("state");

      console.log("OAuth callback parameters:", {
        hasCode: !!code,
        hasState: !!state,
      });

      if (!code) {
        // Check for implicit flow tokens in URL fragment as fallback
        const fragment = window.location.hash.substring(1);
        const fragmentParams = new URLSearchParams(fragment);
        const accessToken = fragmentParams.get("access_token");

        if (accessToken) {
          console.log(
            "Found access token in URL fragment, using implicit flow"
          );
          return await this.handleImplicitFlowCallback(
            accessToken,
            fragmentParams
          );
        }

        return {
          success: false,
          error:
            "No authorization code or access token received from OAuth provider",
        };
      }

      // PKCE flow - send code to backend for processing
      console.log("Processing PKCE flow with authorization code");

      const response = await safeFetch("/api/auth/oauth-callback", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ code, state }),
      });

      const result = await response.json();

      if (result.success && result.token && result.user) {
        console.log("OAuth callback successful, storing token");
        this.setToken(result.token, true);
        return { success: true, user: result.user };
      }

      console.error("OAuth callback failed:", result);
      return {
        success: false,
        error:
          result.details || result.error || "Failed to process OAuth callback",
      };
    } catch (error) {
      console.error("OAuth callback error:", error);
      return {
        success: false,
        error: "Network error during OAuth callback processing",
      };
    }
  }

  private async handleImplicitFlowCallback(
    accessToken: string,
    fragmentParams: URLSearchParams
  ): Promise<AuthResult> {
    try {
      const refreshToken = fragmentParams.get("refresh_token");
      const expiresIn = fragmentParams.get("expires_in");

      // Send the access token to backend for validation and user profile handling
      const response = await safeFetch("/api/auth/oauth-user", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_in: expiresIn,
        }),
      });

      const result = await response.json();

      if (result.success && result.user) {
        this.setToken(accessToken, true);
        return { success: true, user: result.user };
      }

      return {
        success: false,
        error: result.error || "Failed to validate OAuth token",
      };
    } catch (error) {
      console.error("Implicit flow callback error:", error);
      return {
        success: false,
        error: "Failed to process implicit flow callback",
      };
    }
  }
}

export const authService = new AuthService();
