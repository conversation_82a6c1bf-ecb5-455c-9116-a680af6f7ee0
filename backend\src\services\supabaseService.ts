import { createClient } from "@supabase/supabase-js";
import { UserProfile } from "../../../shared/types";
import { SUBSCRIPTION_LIMITS } from "../../../shared/constants";

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export const supabase = createClient(supabaseUrl, supabaseServiceKey);

export class SupabaseService {
  async createUserProfile(
    userId: string,
    email: string,
    name: string
  ): Promise<UserProfile> {
    const { data, error } = await supabase
      .from("users")
      .insert({
        id: userId,
        email,
        name,
        subscription_tier: "Free",
        credits_remaining: SUBSCRIPTION_LIMITS.Free.creditsPerMonth, // 25 credits for freemium
        is_active: true,
        subscription_expires_at: null, // Freemium doesn't expire
        last_credit_reset: new Date().toISOString(), // Track when credits were last reset
      })
      .select()
      .single();

    if (error || !data) {
      throw new Error(error?.message ?? "Failed to create user profile");
    }

    return data;
  }

  async getUserProfile(userId: string): Promise<UserProfile | null> {
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) {
      if ((error as any).code === "PGRST116") return null;
      throw new Error(error.message);
    }

    return data;
  }

  async updateUserProfile(
    userId: string,
    updates: Partial<UserProfile>
  ): Promise<UserProfile> {
    const { data, error } = await supabase
      .from("users")
      .update(updates)
      .eq("id", userId)
      .select()
      .single();

    if (error || !data) {
      throw new Error(error?.message ?? "Failed to update user profile");
    }

    return data;
  }

  // Check if freemium user needs monthly credit reset
  async checkFreemiumReset(userId: string): Promise<boolean> {
    const user = await this.getUserProfile(userId);
    if (!user || user.subscription_tier !== "Free") {
      return false;
    }

    const lastReset = new Date(user.last_credit_reset || user.created_at);
    const now = new Date();
    const daysSinceReset = Math.floor(
      (now.getTime() - lastReset.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Reset credits if it's been 30+ days since last reset
    return daysSinceReset >= 30;
  }

  // Reset freemium user's monthly credits
  async resetFreemiumCredits(userId: string): Promise<void> {
    const { error } = await supabase
      .from("users")
      .update({
        credits_remaining: SUBSCRIPTION_LIMITS.Free.creditsPerMonth,
        last_credit_reset: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId)
      .eq("subscription_tier", "Free");

    if (error) {
      throw new Error(`Failed to reset freemium credits: ${error.message}`);
    }

    // Log the credit reset transaction
    await supabase.from("credit_transactions").insert({
      user_id: userId,
      credits_used: -SUBSCRIPTION_LIMITS.Free.creditsPerMonth, // Negative for addition
      operation_type: "freemium_monthly_reset",
      description: `Monthly freemium credit reset: ${SUBSCRIPTION_LIMITS.Free.creditsPerMonth} credits`,
      metadata: { reset_date: new Date().toISOString() },
      created_at: new Date().toISOString(),
    });
  }

  // Update user profile
  async updateUserProfile(
    userId: string,
    updates: Partial<UserProfile>
  ): Promise<UserProfile> {
    const { data, error } = await supabase
      .from("users")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId)
      .select()
      .single();

    if (error || !data) {
      throw new Error(error?.message ?? "Failed to update user profile");
    }

    return data;
  }

  // Verify JWT token and get user
  async verifyToken(token: string) {
    return await supabase.auth.getUser(token);
  }
}

export const supabaseService = new SupabaseService();
